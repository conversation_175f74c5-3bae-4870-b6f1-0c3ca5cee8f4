import { execute, query } from '../utils/db.js'

export type ConversationType = 'private' | 'group'
export type MessageRole = 'user' | 'assistant' | 'system'
export type MessageType = 'text' | 'image' | 'audio' | 'video' | 'file' | 'event'

export interface ConversationRecord {
  id?: number
  conversation_id: string
  conversation_type: ConversationType
  room_id?: string | null
  room_name?: string | null
  user_id: string
  user_name?: string | null
  role: MessageRole
  message_type: MessageType
  content?: string | null
  content_url?: string | null
  metadata?: any
  model?: string | null
  status_code?: number | null
  duration_ms?: number | null
  parent_message_id?: number | null
  trace_id?: string | null
  ip_address?: string | null
  device_info?: string | null
}

export async function insertConversationRecord(rec: ConversationRecord): Promise<void> {
  await execute(
    `INSERT INTO conversation_record (
      conversation_id, conversation_type, room_id, room_name,
      user_id, user_name, role,
      message_type, content, content_url, metadata,
      model, status_code, duration_ms,
      parent_message_id, trace_id,
      ip_address, device_info
    ) VALUES (
      :conversation_id, :conversation_type, :room_id, :room_name,
      :user_id, :user_name, :role,
      :message_type, :content, :content_url, CAST(:metadata AS JSON),
      :model, :status_code, :duration_ms,
      :parent_message_id, :trace_id,
      :ip_address, :device_info
    )`,
    rec
  )
}

export async function listConversationRecords(conversationId: string, limit = 50): Promise<ConversationRecord[]> {
  return query<ConversationRecord>(
    'SELECT * FROM conversation_record WHERE conversation_id = :conversationId ORDER BY created_at DESC LIMIT :limit',
    { conversationId, limit }
  )
}
