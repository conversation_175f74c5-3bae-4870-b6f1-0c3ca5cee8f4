# 同济智能助手

基于企业微信的智能对话机器人，支持多种智能体切换，提供智能问答服务。

## 项目特性

- **多智能体支持**: 支持同济平台、信息办助手、Go 流式等多种智能体
- **群聊@回复**: 群聊中@机器人提问，机器人会@提问者进行回复
- **模块化架构**: 按智能体分层组织代码，便于维护和扩展
- **配置分离**: 所有配置变量独立管理，支持环境变量
- **企业微信集成**: 基于 Wechaty 框架，支持企业微信所有功能

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置设置

编辑各智能体的配置文件，替换 API 密钥：

- `src/agents/tongji/config.ts` - 同济平台智能体配置
- `src/agents/info/config.ts` - 信息办助手配置
- `src/agents/go/config.ts` - Go 流式智能体配置
- `src/config/index.ts` - 全局配置

### 3. 运行项目

```bash
npm start
```

## 使用说明

### 群聊使用

- @机器人提问，机器人会@你进行回复
- 使用 `设置智能体 tongji` 切换智能体
- 发送 `帮助` 查看使用说明

### 私聊使用

- 直接发送消息给机器人
- 支持所有智能体管理命令
- 提供多种测试功能

## 项目结构

```
src/
├── agents/                    # 智能体模块
│   ├── tongji/               # 同济平台智能体
│   ├── info/                 # 信息办助手
│   ├── go/                   # Go流式智能体
│   └── utils/                # 共享工具
├── config/                   # 配置管理
├── handlers/                 # 消息处理器
├── utils/                    # 工具函数
└── index.ts                  # 主入口
```

## 详细文档

查看 [USAGE.md](./USAGE.md) 获取完整的使用说明和开发指南。

## 部署

支持 Docker 部署，查看 `docker/` 目录下的配置文件。

- [wechaty](https://wechaty.js.org/)

---

![wechaty-logo](wechaty-logo.svg)
