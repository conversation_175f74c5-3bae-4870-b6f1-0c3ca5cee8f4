# 同济智能助手使用说明书

## 项目概述

同济智能助手是一个基于企业微信的智能对话机器人，支持多种智能体切换，提供智能问答服务。

## 项目结构

```
src/
├── agents/                    # 智能体模块
│   ├── tongji/               # 同济同学智能体
│   │   ├── config.ts         # 配置
│   │   ├── agent.ts          # 智能体实现
│   │   └── api.ts            # API调用
│   ├── info/                 # 信息办助手智能体
│   │   ├── config.ts
│   │   ├── agent.ts
│   │   └── api.ts
│   ├── go/                   # Go流式智能体
│   │   ├── config.ts
│   │   ├── agent.ts
│   │   └── api.ts
│   ├── types.ts              # 类型定义
│   ├── registry.ts           # 智能体注册表
│   └── utils/
│       └── session.ts        # 会话管理
├── config/
│   └── index.ts              # 全局配置
├── handlers/
│   └── messageHandler.ts     # 消息处理器
├── utils/
│   ├── commands.ts           # 命令解析
│   ├── keywords.ts           # 关键词处理
│   └── verifyCode.ts         # 验证码处理
└── index.ts                  # 主入口文件
```

## 安装和运行

### 1. 环境要求

- Node.js 16+
- npm 或 yarn
- 企业微信账号

### 2. 安装依赖

```bash
npm install
```

### 3. 配置设置

#### 3.1 修改智能体配置

编辑各智能体的配置文件：

**同济同学智能体** (`src/agents/tongji/config.ts`):

```typescript
export const TONGJI_CONFIG = {
  api: {
    baseUrl: 'https://agent.tongji.edu.cn/api/proxy/api/v1',
    appId: 'your_app_id_here',
    appKey: 'your_app_key_here',
    userId: 'your_user_id'
  }
}
```

**信息办助手** (`src/agents/info/config.ts`):

```typescript
export const INFO_CONFIG = {
  api: {
    baseUrl: 'https://agent.tongji.edu.cn/api/proxy/api/v1',
    appId: 'your_app_id_here',
    apiKey: 'your_api_key_here',
    userId: 'your_user_id'
  }
}
```

**Go 流式智能体** (`src/agents/go/config.ts`):

```typescript
export const GO_CONFIG = {
  api: {
    baseUrl: 'http://your_go_service_ip',
    port: '8084'
  }
}
```

#### 3.2 修改全局配置

编辑 `src/config/index.ts`:

```typescript
export const GLOBAL_CONFIG = {
  wechaty: {
    token: 'your_wechaty_token_here'
    // ... 其他配置
  }
}
```

### 4. 运行项目

```bash
npm start
```

## 使用说明

### 1. 群聊使用

#### 1.1 基本对话

- 在群聊中@机器人并提问，机器人会使用当前智能体回答
- 机器人会@提问者进行回复

#### 1.2 智能体管理

- **默认智能体**: `tongji-同济同学`
- **设置智能体**: `/agent set tongji`
- **查看当前智能体**: `/agent current`
- **列出所有智能体**: `/agent list`

#### 1.3 关键词回复

- 发送 `帮助` 或 `help` 查看使用说明

### 2. 私聊使用

#### 2.1 基本对话

- 直接发送消息给机器人，机器人会使用当前智能体回答

#### 2.2 智能体管理

- 与群聊相同的命令格式

#### 2.3 测试功能

- `urlLink` - 测试链接消息
- `image` - 测试图片消息
- `attachment` - 测试附件消息
- `miniProgram` - 测试小程序消息
- `location` - 测试位置消息

### 3. 智能体说明

#### 3.1 同济同学智能体 (tongji)

- 使用同济大学官方 API
- 支持多轮对话
- 默认智能体

#### 3.2 信息办助手 (info)

- 测试用智能体
- 独立的会话管理

#### 3.3 Go 流式智能体 (go)

- 使用 Go 服务进行流式对话
- 支持实时流式输出
- 需要独立的 Go 服务

## 开发指南

### 1. 添加新智能体

1. 在 `src/agents/` 下创建新目录
2. 创建 `config.ts` 配置文件
3. 创建 `agent.ts` 智能体实现
4. 创建 `api.ts` API 调用逻辑
5. 在 `src/agents/registry.ts` 中注册新智能体

### 2. 修改配置

- 全局配置: `src/config/index.ts`
- 智能体配置: `src/agents/{agent_name}/config.ts`

### 3. 添加新功能

- 消息处理: `src/handlers/messageHandler.ts`
- 工具函数: `src/utils/`
- 命令解析: `src/utils/commands.ts`

## 部署说明

### 1. Docker 部署

项目包含 Docker 配置文件，支持容器化部署：

```bash
# 构建镜像
docker build -t tongji-bot .

# 运行容器
docker run -d --name tongji-bot tongji-bot
```

### 2. 环境变量

支持以下环境变量：

- `IS_DOCKER` - 是否在 Docker 环境中运行
- `VERIFY_CODE` - 验证码（Docker 环境）

## 故障排除

### 1. 常见问题

**Q: 机器人无法启动** A: 检查 Wechaty Token 是否正确，网络连接是否正常

**Q: 智能体切换失败** A: 检查智能体 ID 是否正确，配置文件是否完整

**Q: API 调用失败** A: 检查 API Key 和 URL 配置是否正确

### 2. 日志查看

项目会输出详细的日志信息，包括：

- 消息接收日志
- API 调用日志
- 错误信息

### 3. 联系支持

如遇到问题，请联系项目维护者或查看项目文档。

## 更新日志

### v2.0.0

- 支持切换智能体
- 项目结构，按智能体分层
- 实现群聊@回复功能

### v1.0.0

- 初始版本
- 基础智能体功能
- 企业微信集成
