import { Room, Contact } from '@juzi/wechaty'

// 权限管理工具
export class PermissionManager {
  // 检查是否为群主
  static async isRoomOwner(room: Room, talker: Contact): Promise<boolean> {
    try {
      const owner = await room.owner()
      return !!(owner && owner.id === talker.id)
    } catch (error) {
      console.error('检查群主权限失败:', error)
      return false
    }
  }

  // 检查是否为管理员
  static async isRoomAdmin(room: Room, talker: Contact): Promise<boolean> {
    try {
      return false
    } catch (error) {
      console.error('检查管理员权限失败:', error)
      return false
    }
  }

  // 检查是否有agent命令权限（群主或管理员）
  static async hasAgentCommandPermission(room: Room, talker: Contact): Promise<boolean> {
    try {
      const isOwner = await this.isRoomOwner(room, talker)
      const isAdmin = await this.isRoomAdmin(room, talker)
      return isOwner || isAdmin
    } catch (error) {
      console.error('检查agent命令权限失败:', error)
      return false
    }
  }

  // 检查是否为私聊（私聊默认有权限）
  static isPrivateChat(room: Room | null): boolean {
    return room === null
  }

  // 检查是否有权限执行agent命令
  static async canExecuteAgentCommand(room: Room | null, talker: Contact): Promise<boolean> {
    if (this.isPrivateChat(room)) {
      return true
    }
    if (room) {
      return await this.hasAgentCommandPermission(room, talker)
    }

    return false
  }
}
