import axios from 'axios'
import { GO_CONFIG } from './config.js'
import { Message, Contact, Room } from '@juzi/wechaty'

export async function chatQueryGoStreaming(
  id: string, 
  query: string, 
  message: Message, 
  talker: Contact
): Promise<string> {
  const url = `${GO_CONFIG.api.baseUrl}:${GO_CONFIG.api.port}/chat_query_v2`
  const headers = {
    ID: id
  }
  const data = {
    Query: query
  }

  try {
    console.log('开始发送Go流式AI请求...')

    const response = await new Promise<any>((resolve, reject) => {
      const req = axios.post(url, data, {
        headers: headers,
        responseType: 'stream'
      })
      req.then((res) => resolve(res.data as any)).catch(reject)
    })

    let start = false
    let end = false
    let res = ''

    return new Promise((resolve, reject) => {
      response.on('data', async (chunk: any) => {
        const decodedChunk = chunk.toString('utf-8')
        const lines = decodedChunk.split('\n')
        
        for (let i = 0; i < lines.length; i++) {
          const currentLine = lines[i]
          console.log('当前行: ', currentLine)
          
          if (end) break
          
          if (currentLine.includes('event: message_output_start')) start = true
          if (currentLine.includes('event: message_output_end')) end = true
          
          if (start && !end) {
            if (currentLine.startsWith('data:')) {
              const jsonStr = currentLine.replace('data:', '').trim()
              res += jsonStr
              
              if (res.includes('。') && jsonStr !== '') {
                console.log('内容: ', jsonStr)
                const splitStr = res.split('。')
                await message.say(`@${talker.name()} ${splitStr[0]}`)
                res = splitStr[1]
                console.log('输出内容句号前: ', splitStr[0])
                console.log('剩余内容: ', splitStr[1])
              }
            }
          }
        }
      })

      response.on('end', async () => {
        console.log('数据接收完成')
        console.log('返回数据: ', res)
        if (res !== '') {
          await message.say(`@${talker.name()} ${res}`)
        }
        resolve(res)
      })

      response.on('error', (error) => {
        reject(error)
      })
    })

  } catch (error: any) {
    console.error('Go流式请求发生错误:', error)
  }
}
