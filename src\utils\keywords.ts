import { Contact } from '@juzi/wechaty'
import { GLOBAL_CONFIG } from '../config/index.js'
import { tongjiApiService } from './tongjiApi.js'
import { containsEndSessionKeyword, endConversation } from './conversationState.js'
import { getSessionKey } from '../agents/utils/session.js'
import { logUserMessage } from '../services/conversationLog.js'

// 关键词处理
export async function checkAndHandleKeyword(room: any, talker: Contact, text: string): Promise<boolean> {
  if (!text) return false
  
  // 检查是否是结束会话关键词
  if (containsEndSessionKeyword(text)) {
    const sessionKey = getSessionKey(room, talker)
    
    // 记录用户消息
    if (room) {
      await logUserMessage({
        room: () => room,
        talker: () => talker,
        text: () => text,
        type: () => 1
      } as any)
    } else {
      await logUserMessage({
        room: () => null,
        talker: () => talker,
        text: () => text,
        type: () => 1
      } as any)
    }
    
    // 结束会话
    endConversation(sessionKey)
    
    // 发送确认消息
    const content = typeof GLOBAL_CONFIG.keywords[1].reply === 'function' 
      ? (GLOBAL_CONFIG.keywords[1].reply as any)({ talker, room, text }) 
      : GLOBAL_CONFIG.keywords[1].reply

    if (room) {
      await room.say(content)
    } else {
      await talker.say(content)
    }
    return true
  }
  
  const tongjiResult = await tongjiApiService.handleTongjiQuery(text, talker)
  if (tongjiResult) {
    if (room) {
      await room.say(tongjiResult)
    } else {
      await talker.say(tongjiResult)
    }
    return true
  }

  for (const item of GLOBAL_CONFIG.keywords) {
    const matched = typeof item.keyword === 'string' ? text.includes(item.keyword) : (item.keyword as RegExp).test(text)

    if (matched) {
      const content = typeof item.reply === 'function' ? (item.reply as any)({ talker, room, text }) : item.reply

      if (room) {
        await room.say(content)
      } else {
        await talker.say(content)
      }
      return true
    }
  }
  return false
}
