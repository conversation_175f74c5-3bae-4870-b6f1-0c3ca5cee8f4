stages:
  - copy
  - deploy

rebot-copy:
  image: ringcentral/sshpass
  stage: copy
  script:
    - "sshpass -p $PASSWORD scp -P $PORT -o StrictHostKeyChecking=no -r .devcontainer/ src/ docker/ go/ package.json tsconfig.json agentfunc.ts $USER@$IPADDRESS:/home/<USER>/"
  tags:
    - unicom-docker

rebot-deploy:
  image: ringcentral/sshpass
  stage: deploy
  script:
    - "sshpass -p $PASSWORD ssh -p $PORT -o StrictHostKeyChecking=no -t $USER@$IPADDRESS 'cd /home/<USER>/docker-run-typescript.sh'"
  tags:
    - unicom-docker