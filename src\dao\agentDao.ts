import { query } from '../utils/db.js'

export interface AgentRow {
  appid: string
  id: string
  name: string
  appkey: string
  base_url: string
  description?: string | null
}

export async function getAgentById(id: string): Promise<AgentRow | null> {
  const rows = await query<AgentRow>('SELECT * FROM agent WHERE id = :id LIMIT 1', { id })
  return rows[0] || null
}

export async function getListAgents(): Promise<AgentRow[]> {
  return query<AgentRow>('SELECT * FROM agent ORDER BY updated_at DESC')
}
