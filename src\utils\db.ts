import mysql from 'mysql2/promise'
import { GLOBAL_CONFIG } from '../config/index.js'

export const pool = mysql.createPool({
  host: GLOBAL_CONFIG.mysql.host,
  port: GLOBAL_CONFIG.mysql.port,
  user: GLOBAL_CONFIG.mysql.user,
  password: GLOBAL_CONFIG.mysql.password,
  database: GLOBAL_CONFIG.mysql.database,
  waitForConnections: true,
  connectionLimit: GLOBAL_CONFIG.mysql.connectionLimit,
  queueLimit: 0,
  namedPlaceholders: true
})

export async function query<T = any>(sql: string, params?: any): Promise<T[]> {
  try {
    const [rows] = await pool.query(sql, params)
    return rows as T[]
  } catch (e: any) {
    console.error('MySQL query error:', e?.message, { sql, params })
    throw e
  }
}

export async function execute(sql: string, params?: any): Promise<void> {
  await pool.execute(sql, params)
}
