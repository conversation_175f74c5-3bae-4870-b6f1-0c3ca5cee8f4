import { insertConversationRecord, ConversationType, MessageRole, MessageType } from '../dao/conversationDao.js'
import { Room, Contact, Message } from '@juzi/wechaty'

export async function logUserMessage(message: Message): Promise<void> {
  const room = message.room()
  const talker = message.talker()
  const text = message.text()

  const rec = {
    conversation_id: room ? room.id : talker.id,
    conversation_type: (room ? 'group' : 'private') as ConversationType,
    room_id: room ? room.id : null,
    room_name: room ? await room.topic() : null,
    user_id: talker.id,
    user_name: talker.name(),
    role: 'user' as MessageRole,
    message_type: 'text' as MessageType,
    content: text,
    content_url: null,
    metadata: null,
    model: null,
    status_code: null,
    duration_ms: null,
    parent_message_id: null,
    trace_id: null,
    ip_address: null,
    device_info: null
  }

  await insertConversationRecord(rec)
}

export async function logAssistantText(room: Room | null, talker: Contact, content: string, model?: string): Promise<void> {
  const rec = {
    conversation_id: room ? room.id : talker.id,
    conversation_type: (room ? 'group' : 'private') as ConversationType,
    room_id: room ? room.id : null,
    room_name: room ? await room.topic() : null,
    user_id: talker.id,
    user_name: talker.name(),
    role: 'assistant' as MessageRole,
    message_type: 'text' as MessageType,
    content,
    content_url: null,
    metadata: null,
    model: model || null,
    status_code: 200,
    duration_ms: null,
    parent_message_id: null,
    trace_id: null,
    ip_address: null,
    device_info: null
  }

  await insertConversationRecord(rec)
}
