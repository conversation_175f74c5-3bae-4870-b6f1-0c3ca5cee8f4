# 项目架构说明

## 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    项目架构                                  │
├─────────────────────────────────────────────────────────────┤
│  Entry Point: src/index.ts                                  │
│  ├── 机器人初始化和事件监听                                   │
│  ├── 二维码扫描处理                                          │
│  └── 消息路由分发                                            │
├─────────────────────────────────────────────────────────────┤
│  Message Handlers: src/handlers/                            │
│  ├── messageHandler.ts - 消息处理器                          │
│  │   ├── handleGroupMessage() - 群聊处理                     │
│  │   ├── handlePrivateMessage() - 私聊处理                   │
│  │   └── handleAIResponse() - AI对话处理                     │
├─────────────────────────────────────────────────────────────┤
│  Agents Layer: src/agents/                                  │
│  ├── tongji/ - 同济同学智能体                                │
│  │   ├── config.ts - 配置                                   │
│  │   ├── agent.ts - 智能体实现                               │
│  │   └── api.ts - API调用                                   │
│  ├── info/ - 信息办助手（测试）                               │
│  │   ├── config.ts                                          │
│  │   ├── agent.ts                                           │
│  │   └── api.ts                                             │
│  ├── go/ - Go流式智能体                                      │
│  │   ├── config.ts                                          │
│  │   ├── agent.ts                                           │
│  │   └── api.ts                                             │
│  ├── types.ts - 类型定义                                     │
│  ├── registry.ts - 智能体注册表                              │
│  └── utils/session.ts - 会话管理                             │
├─────────────────────────────────────────────────────────────┤
│  Configuration: src/config/                                │
│  └── index.ts - 全局配置管理                                 │
├─────────────────────────────────────────────────────────────┤
│  Utilities: src/utils/                                      │
│  ├── commands.ts - 命令解析                                  │
│  ├── keywords.ts - 关键词处理                                │
│  └── verifyCode.ts - 验证码处理                              │
└─────────────────────────────────────────────────────────────┘
```
