import axios from 'axios'
import { GLOBAL_CONFIG } from '../config/index.js'

// 同济学工信息API响应类型
export interface TongjiUserInfo {
  deptCode: string
  deptName: string
  name: string
  pid: string
  statusCode: string
  statusName: string
  userId: string
  userTypeCode: string
  userTypeName: string
}

export interface TongjiApiResponse {
  code: string
  data: {
    count: number
    userInfos: TongjiUserInfo[]
    sincePid: string
  }
  msg: string
}

// 学工信息查询关键字
export const TONGJI_KEYWORDS = ['学号', '学工号', '工号', '手机号', '部门', '学院', '姓名']

// 同济API服务类
export class TongjiApiService {
  private baseUrl: string
  private timeout: number

  constructor() {
    this.baseUrl = GLOBAL_CONFIG.tongjiApi.USER_PERSON_INFO_BY_PID_URL
    this.timeout = GLOBAL_CONFIG.tongjiApi.timeout
  }

  // 根据PID查询学工信息
  async getPersonInfoByPid(pid: string): Promise<TongjiUserInfo[] | null> {
    try {
      const response = await axios.get<TongjiApiResponse>(this.baseUrl, {
        params: { pid },
        timeout: this.timeout
      })

      if (response.data.code === 'A00000' && response.data.data.userInfos) {
        const userInfos = response.data.data.userInfos
        console.log('API查询成功，已缓存:', pid)
        return userInfos
      } else {
        console.error('API返回错误:', response.data.msg)
        return null
      }
    } catch (error) {
      console.error('查询学工信息失败:', error)
      return null
    }
  }

  formatUserInfo(userInfos: TongjiUserInfo[]): string {
    if (!userInfos || userInfos.length === 0) {
      return '未找到相关学工信息'
    }

    let result = '学工信息查询结果：\n\n'

    userInfos.forEach((user, index) => {
      result += `姓名：${user.name}\n`
      result += `学工号：${user.pid}\n`
      result += `用户ID：${user.userId}\n`
      result += `部门：${user.deptName} (${user.deptCode})\n`
      result += `用户类型：${user.userTypeName}\n`
      result += `状态：${user.statusName}\n`
      if (index < userInfos.length - 1) {
        result += '\n---\n\n'
      }
    })

    return result
  }

  // 检查文本是否包含学工信息查询关键字
  containsTongjiKeywords(text: string): boolean {
    return TONGJI_KEYWORDS.some((keyword) => text.includes(keyword))
  }

  // 从文本中取PID
  extractPidFromText(text: string): string[] {
    const pidPattern = /\b\d{8,}\b/g
    const matches = text.match(pidPattern)
    return matches || []
  }

  // 从talker中取PID
  extractPidFromTalker(talker: any): string[] {
    const pids: string[] = []

    try {
      // talker的payload中的handle和wexin字段
      if (talker.payload) {
        const payload = talker.payload
        if (payload.handle && typeof payload.handle === 'string') {
          pids.push(payload.handle)
        }
        if (payload.wexin && typeof payload.wexin === 'string') {
          pids.push(payload.wexin)
        }
      }
    } catch (error) {
      console.error('从talker提取PID失败:', error)
    }

    return pids
  }

  // 处理学工信息查询
  async handleTongjiQuery(text: string, talker?: any): Promise<string | null> {
    if (!this.containsTongjiKeywords(text)) {
      return null
    }

    let pids = this.extractPidFromText(text)

    if (pids.length === 0 && talker) {
      pids = this.extractPidFromTalker(talker)
    }

    if (pids.length === 0) {
      return '未找到有效的学工号'
    }

    let result = ''
    for (const pid of pids) {
      console.log('从文本提取的PID:', pids)
      const userInfos = await this.getPersonInfoByPid(pid)
      if (userInfos) {
        result += this.formatUserInfo(userInfos)
        if (pids.length > 1) {
          result += '\n\n'
        }
      } else {
        result += `查询学工号基本信息失败\n\n`
      }
    }

    return result.trim()
  }
}

export const tongjiApiService = new TongjiApiService()
