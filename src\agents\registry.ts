import { Agent } from './types.js'
import { getAgentById, getListAgents } from '../dao/agentDao.js'
import { getSessionKey } from './utils/session.js'
import { createConversationForAgent, chatQueryStreamingForAgent } from './utils/commonApi.js'

export async function listAgents(): Promise<string> {
  const agents = await getListAgents()
  return agents.map((a) => `- ${a.id}: ${a.name}`).join('\n')
}

export async function getAgent(agentId: string): Promise<Agent> {
  let row
  try {
    row = await getAgentById(agentId)

    if (!row) {
      throw new Error(`Agent ${agentId} not found`)
    }
  } catch (error) {
    console.error(`获取智能体 ${agentId} 失败:`, error)
    throw error
  }

  const agent: Agent = {
    id: row.id,
    name: row.name,
    kind: row.description || '',
    description: row.description || '',
    respond: async (query, ctx) => {
      const { room, talker } = ctx
      const sessionKey = getSessionKey(room || null, talker)

      const conversationId = await createConversationForAgent(row.id, sessionKey)

      if (!conversationId) {
        const errorMsg = '抱歉，无法创建对话会话'
        if (room) {
          await room.say(`@${talker.name()} ${errorMsg}`)
        } else {
          await ctx.message.say(errorMsg)
        }
        return
      }

      let full = ''
      for await (const chunk of chatQueryStreamingForAgent(row.id, conversationId, query)) {
        full += chunk
      }

      if (room) {
        await room.say(`@${talker.name()} ${full}`)
      }
      return full
    }
  }

  return agent
}
