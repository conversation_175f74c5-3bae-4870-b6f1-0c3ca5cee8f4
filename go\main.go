package main

import (
	"log"
	"net/http"
	// "time"
)

func main() {
	// 设置路由
	// http.HandleFunc("/create_conversation", createConversationHandler) // 获取ConversationId
	http.HandleFunc("/chat_query_v2", chatQueryHandler) // 聊天

	// 启动服务器
	server := &http.Server{
		Addr: ":8080",
		// ReadTimeout:  5 * time.Second,
		// WriteTimeout: 10 * time.Second,
	}

	log.Println("Starting API server on :8080")
	if err := server.ListenAndServe(); err != nil {
		log.Fatal("Server failed to start: ", err)
	}
}
