import { execute, query } from '../utils/db.js'

export interface GroupConfig {
  id?: number
  room_id: string
  agent_id?: string | null
  allow_agent_switch?: number
  enable_push?: number
  enable_blacklist?: number
}

export async function getGroupConfig(roomId: string): Promise<GroupConfig | null> {
  const rows = await query<GroupConfig>('SELECT * FROM group_config WHERE room_id = :roomId LIMIT 1', { roomId })
  return rows[0] || null
}

// TODO 群配置默认全部开启
export async function upsertGroupConfig(cfg: GroupConfig): Promise<void> {
  const finalCfg = {
    agent_id: cfg.agent_id ?? null,
    allow_agent_switch: 1,
    enable_push: 1,
    enable_blacklist: 1,
    ...cfg
  }
  await execute(
    `INSERT INTO group_config (room_id, agent_id, allow_agent_switch, enable_push, enable_blacklist)
     VALUES (:room_id, :agent_id, :allow_agent_switch, :enable_push, :enable_blacklist)
     ON DUPLICATE KEY UPDATE 
       agent_id = VALUES(agent_id),
       allow_agent_switch = VALUES(allow_agent_switch),
       enable_push = VALUES(enable_push),
       enable_blacklist = VALUES(enable_blacklist)`,
    finalCfg
  )
}
