# 持久化功能说明

## 新增功能

### 1. 智能体设置持久化
- 使用Redis存储群聊和私聊的智能体设置
- 服务重启后设置不会丢失
- 支持每个群聊/私聊独立设置智能体

### 2. 群聊信息持久化
- 自动保存群聊基本信息（群名、成员数、群主等）
- 支持群聊信息查询和管理
- 实时更新群聊状态

### 3. 智能体配置持久化
- 智能体配置参数从Redis数据库获取
- 支持动态更新智能体配置
- 配置变更实时生效

### 4. 同济学工信息查询
- 集成同济大学学工信息API：`https://api.tongji.edu.cn/v2/dc/user/person_info_by_pid`
- 支持关键字查询：学号、学工号、工号、手机号、部门、学院、姓名
- 自动缓存查询结果，提高响应速度
- 支持从talker的`handle`和`wexin`字段提取PID
- 支持群聊和私聊中的学工信息查询

### 5. 权限控制
- 群聊中只有群主和管理员可以使用agent命令
- 私聊默认有所有权限
- 权限检查实时生效

## 环境配置

### Redis配置
在环境变量中设置Redis连接信息：

```bash
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password
REDIS_DB=0
```

### 使用方式

#### 智能体命令（仅群主/管理员可用）
- `/agent set <智能体ID>` - 设置当前智能体
- `/agent list` - 列出所有可用智能体
- `/agent current` - 查看当前智能体

#### 学工信息查询
发送包含以下关键字的消息：
- 学号、学工号、工号、手机号、部门、学院、姓名

例如：
- "查询学号 20240001"
- "学工号 12345678"
- "部门 计算机学院"

## 技术实现

### 存储结构
- `agent:setting:{sessionKey}` - 智能体设置
- `room:info:{roomId}` - 群聊信息
- `agent:config:{agentId}` - 智能体配置
- `user:info:{pid}` - 用户信息缓存
- `conversation:{sessionKey}` - 会话ID

### 权限检查
- 群聊：检查是否为群主或管理员
- 私聊：默认有权限
- 实时权限验证

### 缓存策略
- 用户信息缓存24小时
- 群聊信息实时更新
- 智能体设置永久保存
