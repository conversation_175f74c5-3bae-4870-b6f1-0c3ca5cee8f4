{"compilerOptions": {"target": "ES2022", "module": "ESNext", "allowSyntheticDefaultImports": true, "allowJs": true, "moduleResolution": "node", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./", "resolveJsonModule": true, "isolatedModules": true, "sourceMap": true, "lib": ["ES2022"]}, "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}, "include": ["src/index.ts"], "exclude": ["node_modules"]}