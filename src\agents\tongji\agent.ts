import { Agent, AgentRequestContext } from '../types.js'
import { TONGJI_CONFIG } from './config.js'
import { createConversation, chatQueryStreaming } from './api.js'
import { getSessionKey } from '../utils/session.js'

export const tongjiAgent: Agent = {
  id: TONGJI_CONFIG.id,
  name: TONGJI_CONFIG.name,
  kind: TONGJI_CONFIG.kind,
  description: TONGJI_CONFIG.description,
  respond: async (query, ctx) => {
    const { room, talker } = ctx
    const sessionKey = getSessionKey(room || null, talker)
    const conversationId = await createConversation(sessionKey)

    if (!conversationId) {
      const errorMsg = '抱歉，无法创建对话会话'
      if (room) {
        await room.say(`@${talker.name()} ${errorMsg}`)
      } else {
        await ctx.message.say(errorMsg)
      }
      return
    }

    let full = ''
    for await (const chunk of chatQueryStreaming(conversationId, query)) {
      full += chunk
    }

    if (room) {
      await room.say(`@${talker.name()} ${full}`)
    }
    return full
  }
}
