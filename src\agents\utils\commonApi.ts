import axios from 'axios'
import { getAgentById } from '../../dao/agentDao.js'

interface ConversationResponse {
  Conversation?: {
    AppConversationID?: string
  }
}

interface ChatQueryData {
  Query: string
  AppConversationID: string
  AppKey: string
  ResponseMode: string
  UserID: string
}

/**
 * 根据agentId创建会话
 * @param agentId 智能体ID
 * @param sessionKey 会话键
 * @param inputs 可选的输入参数（用于某些特殊智能体）
 * @returns 会话ID或null
 */
export async function createConversationForAgent(agentId: string, sessionKey: string, inputs?: Record<string, any>): Promise<string | null> {
  const agent = await getAgentById(agentId)
  if (!agent) {
    console.error(`Agent ${agentId} not found`)
    return null
  }

  const url = `${agent.base_url}/create_conversation`
  const headers = {
    'Content-Type': 'application/json',
    Apikey: agent.appkey
  }
  const data: any = {
    Appkey: agent.appid,
    UserID: '3'
  }

  // 特殊处理：为info智能体添加Inputs变量
  if (agentId === 'info') {
    const infoInputs = { id: sessionKey.replace('::info', '') }
    if (infoInputs && Object.keys(infoInputs).length > 0) {
      data.Inputs = infoInputs
    }
  }

  // 如果有额外的输入参数，添加到请求数据中
  if (inputs && Object.keys(inputs).length > 0) {
    data.Inputs = { ...data.Inputs, ...inputs }
  }

  try {
    console.log(`为智能体 ${agentId} 创建会话...`, data)
    const response = await axios.post<ConversationResponse>(url, data, { headers })

    if (response.status === 200) {
      const conversationId = response.data.Conversation?.AppConversationID
      if (conversationId) {
        console.log(`智能体 ${agentId} 会话创建成功，ID: ${conversationId}`)
        return conversationId
      }
    } else {
      console.log(`创建会话失败: ${response.status}, ${response.data}`)
    }
  } catch (error: any) {
    console.error(`创建智能体 ${agentId} 会话时发生错误:`, error.response ? error.response.data : error.message)
  }

  return null
}

/**
 * 根据agentId进行流式聊天查询
 * @param agentId 智能体ID
 * @param conversationId 会话ID
 * @param query 查询内容
 * @returns 异步生成器，产生响应片段
 */
export async function* chatQueryStreamingForAgent(agentId: string, conversationId: string, query: string): AsyncGenerator<string> {
  const agent = await getAgentById(agentId)
  if (!agent) {
    console.error(`Agent ${agentId} not found`)
    return
  }

  const url = `${agent.base_url}/chat_query_v2`
  const headers = {
    'Content-Type': 'application/json',
    Apikey: agent.appkey
  }
  const data: ChatQueryData = {
    Query: query,
    AppConversationID: conversationId,
    AppKey: agent.appid,
    ResponseMode: 'blocking',
    UserID: '3'
  }

  try {
    console.log(`开始发送智能体 ${agentId} AI请求...`)
    const response = await new Promise<any>((resolve, reject) => {
      const req = axios.post(url, data, { headers })
      req.then((res) => resolve(res.data as any)).catch(reject)
    })

    yield response.answer
  } catch (error: any) {
    console.error(`智能体 ${agentId} 请求发生错误:`, error)
    yield `处理响应时出错: ${error.message}`
  }
}
