package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
)

var conversations = make(map[string]string) // 存储会话ID和对应状态
var maxConversations = 10

const APIBaseURL = "https://agent.tongji.edu.cn/api/proxy/api/v1"
const APPKey = "cvvgdkuh36688d49d5fg"

// const UserId = "3"

// 获取或创建会话
func getConversation(w http.ResponseWriter, r *http.Request) (string, error) {
	ID := r.Header.Get("ID")
	fmt.Println("ID: ", ID)
	// 检查会话是否已存在
	if status, exists := conversations[ID]; exists {
		return status, nil
	}

	// 检查会话数量限制
	if len(conversations) >= maxConversations {
		return "", fmt.Errorf("maximum conversation limit (%d) reached", maxConversations)
	}

	requestData := map[string]interface{}{
		// "Appkey": APPKey,
		"UserID": ID,
	}
	fmt.Println("requestData: ", requestData)
	jsonData, _ := json.Marshal(requestData)
	httpReq, err := http.NewRequest("POST", APIBaseURL+"/create_conversation", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Apikey", APPKey)
	fmt.Println("header: ", httpReq.Header)
	fmt.Println("body: ", httpReq.Body)
	resp, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var result ConversationResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", err
	}

	fmt.Println("获取ConversationID：", result.Conversation.AppConversationID)
	newID := result.Conversation.AppConversationID
	conversations[ID] = newID // 设置会话状态
	return newID, nil
}

func createConversationHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 使用getConversation管理会话
	conversationID, err := getConversation(w, r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"conversationId": conversationID,
	})
}

type StreamData struct {
	Event  string `json:"event"`
	Answer string `json:"answer"`
}

func chatQueryHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var chatReq ChatQueryRequest
	if err := json.NewDecoder(r.Body).Decode(&chatReq); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	ID := r.Header.Get("ID")

	// 验证会话
	conversationID, err := getConversation(w, r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return
	}

	// 设置流式响应头
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
		return
	}

	// 准备请求数据
	requestData := map[string]interface{}{
		"Query":             chatReq.Query,
		"AppConversationID": conversationID,
		"AppKey":            APPKey,
		"ResponseMode":      "streaming",
		"UserID":            ID,
	}
	jsonData, _ := json.Marshal(requestData)

	// 创建请求
	req, err := http.NewRequest("POST", APIBaseURL+"/chat_query_v2", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Apikey", APPKey)
	req.Header.Set("Accept", "text/event-stream")
	req.Header.Set("responseType", "stream")
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 发送请求
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(line, "data:") {
			// 移除前缀并解析JSON
			jsonData := line[5:] // 假设"data:"后面紧跟JSON字符串
			// fmt.Println("jsonData: ", jsonData)
			trimmedJsonData := strings.TrimSpace(jsonData)
			if trimmedJsonData == "" {
				fmt.Println("jsonData 去除空格后为空")
				// 根据需求处理空数据的情况，比如跳过或发送错误事件
				continue
			}
			var event StreamData
			if err := json.Unmarshal([]byte(jsonData), &event); err != nil {
				// 发送错误事件
				// fmt.Fprintf(w, "event: error\ndata: %s\n\n", err.Error())
				flusher.Flush()
				continue
			}
			// 发送事件到客户端
			cleanedAnswer := strings.Trim(event.Answer, "\n")
			fmt.Fprintf(w, "event: %s\ndata: %s", event.Event, cleanedAnswer)
			flusher.Flush()
		} else if strings.HasPrefix(line, "event:") {
			// 处理其他类型的事件（如果需要）
		}
	}

	if err := scanner.Err(); err != nil {
		// 发送错误事件
		fmt.Fprintf(w, "event: error\ndata: %s\n\n", err.Error())
		flusher.Flush()
	}

}
