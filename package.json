{"type": "module", "name": "wk-tongji-bot", "version": "v0.0.2", "description": "wk-tongji-bot service", "main": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "cross-env WECHATY_PUPPET_SERVICE_AUTHORITY=\"token-service-discovery-test.juzibot.com\" NODE_OPTIONS=\"--no-warnings --loader=ts-node/esm\" node src/index.ts", "build": "tsc", "start:prod": "node start.js", "test:redis": "node test-redis.js"}, "repository": {"type": "git", "url": "git+https://git.tongji.edu.cn/nic-sde/robot.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://git.tongji.edu.cn/nic-sde/robot/-/issues"}, "homepage": "https://git.tongji.edu.cn/nic-sde/robot#readme", "dependencies": {"@grpc/grpc-js": "1.8.12", "@juzi/wechaty": "^1.0.65", "@juzi/wechaty-puppet": "^1.0.61", "@juzi/wechaty-puppet-service": "^1.0.69", "@microsoft/fetch-event-source": "^2.0.1", "axios": "^1.8.4", "cross-env": "^10.0.0", "eventsource": "^3.0.6", "file-box": "^1.5.5", "ioredis": "^5.7.0", "mysql2": "^3.14.5", "qrcode-terminal": "^0.12.0"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/node": "^22.14.1", "@types/qrcode-terminal": "^0.12.0", "cors": "^2.8.5", "express": "^5.1.0", "ioredis": "^5.7.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}