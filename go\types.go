/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-23 09:52:44
 * @LastEditors: xiaohang <EMAIL>
 * @LastEditTime: 2025-04-23 15:03:11
 * @FilePath: /workpro-getting-started-latest-features/go/types.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package main

// 创建会话请求
type CreateConversationRequest struct {
	AppKey string `json:"Appkey"`
	UserID string `json:"UserID"`
}

// 创建会话响应
type ConversationResponse struct {
	Conversation struct {
		AppConversationID string `json:"AppConversationID"`
	} `json:"Conversation"`
}

// 对话查询请求
type ChatQueryRequest struct {
	Query             string `json:"Query"`
	AppConversationID string `json:"AppConversationID"`
	AppKey            string `json:"AppKey"`
	ResponseMode      string `json:"ResponseMode"`
	UserID            string `json:"UserID"`
}

// 对话查询响应
type ChatQueryResponse struct {
	Answer string `json:"answer"`
}
