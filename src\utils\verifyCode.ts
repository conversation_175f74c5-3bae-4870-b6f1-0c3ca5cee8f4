import * as readline from 'readline'
import { GLOBAL_CONFIG } from '../config/index.js'

export const getVerifyCode = (): Promise<string> => {
  // 检查是否在Docker环境
  if (process.env.IS_DOCKER) {
    console.log('请在30秒内通过以下方式提供验证码:')
    console.log('1. 设置环境变量 VERIFY_CODE')
    console.log('2. 访问 http://<容器IP>:3000/verify 提交')

    // 创建HTTP服务器接收验证码
    const http = require('http')
    let verifyCode = ''
    const server = http
      .createServer((req: any, res: any) => {
        if (req.url === '/verify' && req.method === 'POST') {
          let body = ''
          req.on('data', (chunk: any) => (body += chunk))
          req.on('end', () => {
            verifyCode = JSON.parse(body).code
            res.writeHead(200)
            res.end('验证码已接收')
          })
        } else {
          res.writeHead(404)
          res.end()
        }
      })
      .listen(GLOBAL_CONFIG.verifyCode.port)

    return new Promise((resolve) => {
      // 30秒超时
      const timeout = setTimeout(() => {
        server.close()
        resolve('') // 返回空字符串表示超时
      }, GLOBAL_CONFIG.verifyCode.timeout)

      // 每秒检查是否收到验证码
      const interval = setInterval(() => {
        if (verifyCode) {
          clearTimeout(timeout)
          clearInterval(interval)
          server.close()
          resolve(verifyCode.trim())
        }
      }, 1000)
    })
  }

  // 非Docker环境保持原样
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  })

  return new Promise((resolve) => {
    rl.question('请输入收到的验证码: ', (answer) => {
      rl.close()
      resolve(answer.trim())
    })
  })
}
