# 群聊会话管理优化说明

## 问题背景
在群聊中，机器人会在5分钟内自动回复用户的连续对话，但这可能会干扰用户与其他群成员的正常交流。

## 解决方案

### 1. 结束会话关键词
用户可以通过以下关键词主动结束与机器人的对话：
- `结束对话`
- `停止对话`
- `bye`
- `再见`
- `结束`
- `停止`
- `不聊了`
- `结束聊天`
- `停止聊天`
- `退出对话`

**使用方法**：在群聊中发送包含这些关键词的消息，机器人会回复确认并结束对话状态。

### 2. 智能对话检测
系统现在能够智能判断用户是否在与其他群成员对话：

#### 检测机制
- **@符号检测**：如果消息中@了其他群成员，系统会认为你在与其他群成员对话
- **会话状态管理**：系统会跟踪每个用户的对话状态，避免误触发
- **时间窗口**：连续对话检测限制在5分钟内

#### 自动结束场景
当检测到以下情况时，系统会自动结束与机器人的会话：
1. 用户@了其他群成员
2. 用户发送了结束会话的关键词
3. 会话超过5分钟无活动

### 3. 会话状态管理
- **激活状态**：当用户@机器人或发送连续对话时激活
- **自动清理**：超过1小时的会话状态会被自动清理
- **状态持久化**：会话状态在内存中维护，重启后重置

## 使用示例

### 场景1：正常与机器人对话
```
用户: @机器人 你好
机器人: @用户 你好！有什么可以帮助你的吗？

用户: 今天天气怎么样？
机器人: @用户 今天天气晴朗，温度适宜...

用户: 谢谢
机器人: @用户 不客气！
```

### 场景2：结束与机器人的对话
```
用户: @机器人 你好
机器人: @用户 你好！有什么可以帮助你的吗？

用户: 结束对话
机器人: @用户 好的，对话已结束。如需重新开始，请 @我 提问～
```

### 场景3：与其他群成员对话
```
用户: @机器人 你好
机器人: @用户 你好！有什么可以帮助你的吗？

用户: @张三 你吃饭了吗？
# 机器人不会回复，因为检测到你在与张三对话
```

### 场景4：连续对话超时
```
用户: @机器人 你好
机器人: @用户 你好！有什么可以帮助你的吗？

# 6分钟后
用户: 你好
# 机器人不会回复，因为会话已超时
```

## 技术实现

### 核心文件
- `src/utils/conversationState.ts` - 会话状态管理
- `src/handlers/messageHandler.ts` - 消息处理逻辑
- `src/config/index.ts` - 关键词配置

### 关键功能
1. **会话状态跟踪**：记录每个用户的对话状态和最后活动时间
2. **智能关键词检测**：支持多种结束会话的表达方式
3. **群成员对话检测**：通过@符号判断对话对象
4. **自动清理机制**：定期清理过期的会话状态

## 注意事项
1. 会话状态存储在内存中，重启服务后会重置
2. 群成员名称匹配基于包含关系，可能存在误判
3. 建议使用明确的@符号来避免误触发
4. 系统会自动处理大部分场景，无需手动干预
