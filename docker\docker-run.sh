set -e

BOT=$1
BUILD_CUSTOM_IMAGE=$2

# import WECHATY_IMAGE from bin/docker-config.sh
source $(dirname $0)/docker-config.sh

DOCKER_ENV=$(docker::env $(env | grep WECHATY_) )

# 构建自定义镜像
if [ "$BUILD_CUSTOM_IMAGE" = "true" ]; then
  echo "Building custom Docker image..."
  docker build -t wechaty-custom -f $(dirname $0)/Dockerfile .
  WECHATY_IMAGE=wechaty-custom
else
  [ -z "$NO_PULL" ] && docker pull "$WECHATY_IMAGE"
fi

# https://stackoverflow.com/a/911213/1123955
if [ -t 1 ]; then
  TI=' -t -i '
else
  TI=''
fi

docker run \
  -d \
  $TI \
  --rm \
  --name wechaty \
  --mount type=bind,source="$(pwd)",target=/bot \
  $DOCKER_ENV \
  -e WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com \
  "$WECHATY_IMAGE" \
  "$BOT"