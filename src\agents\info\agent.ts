import { Agent, AgentRequestContext } from '../types.js'
import { INFO_CONFIG } from './config.js'
import { createConversation, chatQueryStreaming } from './api.js'
import { getSessionKey } from '../utils/session.js'

export const infoAgent: Agent = {
  id: INFO_CONFIG.id,
  name: INFO_CONFIG.name,
  kind: INFO_CONFIG.kind,
  description: INFO_CONFIG.description,
  respond: async (query, ctx) => {
    const { room, talker } = ctx
    const sessionKey = `${getSessionKey(room || null, talker)}::info`
    const conversationId = await createConversation(sessionKey)

    if (!conversationId) {
      const errorMsg = '抱歉，无法创建信息办助手会话'
      if (room) {
        await room.say(`@${talker.name()} ${errorMsg}`)
      } else {
        await ctx.message.say(errorMsg)
      }
      return
    }

    let full = ''
    for await (const chunk of chatQueryStreaming(conversationId, query)) {
      full += chunk
    }

    if (room) {
      await room.say(`@${talker.name()} ${full}`)
    }
    return full
  }
}
