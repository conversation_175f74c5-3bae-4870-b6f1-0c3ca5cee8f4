import axios from 'axios'
import { Readable } from 'node:stream'
import { IncomingMessage } from 'node:http'

// API配置
const API_BASE_URL = "http://agent.tongji.edu.cn/api/proxy/api/v1"
const USER_ID = "3"

// 定义接口以增强类型安全
interface ConversationResponse {
  Conversation?: {
    AppConversationID?: string
  }
}

interface ChatQueryData {
  Query: string
  AppConversationID: string
  AppKey: string
  ResponseMode: string
  UserID: string
}

interface StreamChunk {
  event?: string
  answer?: string
}

/**
 * 创建一个新的会话并返回会话ID
 * @param appKey API密钥
 * @returns Promise<string | null> 会话ID
 */
export async function createConversation(appKey: string): Promise<string | null> {
  const url = `${API_BASE_URL}/create_conversation`
  const headers = {
    "Content-Type": "application/json",
    "Apikey": appKey
  }
  const data = {
    "Appkey": appKey,
    "UserID": USER_ID
  }

  try {
    const response = await axios.post<ConversationResponse>(url, data, { headers })
    
    if (response.status === 200) {
      const conversationId = response.data.Conversation?.AppConversationID
      console.log(`会话创建成功，ID: ${conversationId}`)
      return conversationId || null
    } else {
      console.log(`创建会话失败: ${response.status}, ${response.data}`)
      return null
    }
  } catch (error: any) {
    console.error('创建会话时发生错误:', error.response ? error.response.data : error.message)
    return null
  }
}

/**
 * 发送聊天请求并处理流式响应
 * @param conversationId 会话ID
 * @param query 查询内容
 * @param appKey API密钥
 * @returns AsyncGenerator<string> 流式响应的生成器
 */
export async function* chatQueryStreaming(
  conversationId: string, 
  query: string, 
  appKey: string
): AsyncGenerator<string> {
  const url = `${API_BASE_URL}/chat_query`
  const headers = {
    "Content-Type": "application/json",
    "Apikey": appKey,
    "Accept": "text/event-stream"
  }
  const data: ChatQueryData = {
    "Query": query,
    "AppConversationID": conversationId,
    "AppKey": appKey,
    "ResponseMode": "streaming",
    "UserID": USER_ID
  }

  try {
    const response = await new Promise<IncomingMessage>((resolve, reject) => {
      const req = axios.post(url, data, {
        headers: headers,
        responseType: 'stream'
      })
      
      req.then(res => resolve(res.data as any)).catch(reject)
    })

    // 使用 async generator 处理流
    for await (const chunk of response as Readable) {
      const decodedChunk = chunk.toString('utf-8')
      
      // 处理 data:data: 开头的行
      if (decodedChunk.startsWith('data:data:')) {
        try {
          // 提取 JSON 字符串，去除 data:data: 前缀
          const jsonStr = decodedChunk.replace('data:data:', '').trim()
          const jsonData: StreamChunk = JSON.parse(jsonStr)
          
          // 如果是消息类型且有答案内容
          if (jsonData.event === 'message' && jsonData.answer) {
            yield jsonData.answer
          }
        } catch (jsonError) {
          console.error('JSON解析错误:', jsonError)
        }
      }
    }
  } catch (error: any) {
    console.error('请求发生错误:', error)
    yield `处理响应时出错: ${error.message}`
  }
}

/**
 * 使用示例函数
 * @param appKey API密钥
 */
export async function example(appKey: string) {
  try {
    const conversationId = await createConversation(appKey)
    
    if (conversationId) {
      console.log(`会话创建成功，ID: ${conversationId}`)
      
      const query = '地球的半径是多少'
      console.log(`开始查询: ${query}`)
      
      let fullResponse = ''
      for await (const chunk of chatQueryStreaming(conversationId, query, appKey)) {
        fullResponse += chunk
        console.log('收到响应片段:', chunk)
      }
      
      console.log('完整响应:', fullResponse)
    } else {
      console.error('会话创建失败')
    }
  } catch (error) {
    console.error('执行过程中发生错误:', error)
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  const testAppKey = process.env.APP_KEY || 'your_app_key_here'
  example(testAppKey).catch(console.error)
}

export {}