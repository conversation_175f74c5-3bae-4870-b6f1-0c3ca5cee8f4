import axios from 'axios'
import { getAgentById } from '../../dao/agentDao.js'

interface ConversationResponse {
  Conversation?: {
    AppConversationID?: string
  }
}

interface ChatQueryData {
  Query: string
  AppConversationID: string
  AppKey: string
  ResponseMode: string
  UserID: string
}

export async function createConversation(sessionKey: string): Promise<string | null> {
  const agent = await getAgentById('tjtx')
  if (!agent) return null

  const url = `${agent.base_url}/create_conversation`
  const headers = {
    'Content-Type': 'application/json',
    Apikey: agent.appkey
  }
  const data = {
    Appkey: agent.appid,
    UserID: '3'
  }

  try {
    const response = await axios.post<ConversationResponse>(url, data, { headers })
    if (response.status === 200) {
      const conversationId = response.data.Conversation?.AppConversationID
      if (conversationId) {
        return conversationId
      }
    } else {
      console.log(`创建会话失败: ${response.status}, ${response.data}`)
    }
  } catch (error: any) {
    console.error('创建会话时发生错误:', error.response ? error.response.data : error.message)
  }
  return null
}

export async function* chatQueryStreaming(conversationId: string, query: string) {
  const agent = await getAgentById('tjtx')
  if (!agent) return

  const url = `${agent.base_url}/chat_query_v2`
  const headers = {
    'Content-Type': 'application/json',
    Apikey: agent.appkey
  }
  const data: ChatQueryData = {
    Query: query,
    AppConversationID: conversationId,
    AppKey: agent.appid,
    ResponseMode: 'blocking',
    UserID: '3'
  }

  try {
    const response = await new Promise<any>((resolve, reject) => {
      const req = axios.post(url, data, { headers })
      req.then((res) => resolve(res.data as any)).catch(reject)
    })

    yield response.answer
  } catch (error: any) {
    console.error('请求发生错误:', error)
  }
}
